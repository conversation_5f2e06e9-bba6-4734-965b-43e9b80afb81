package com.rt.kt.lib.component.priceview

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle

/**
 * @ClassName: PriceText
 * @Description: 价格标签组件
 * @Date: 2025/5/28
 */

/**
 * 价格标签组件 - 对应Android原生PriceView.showPrice()
 *
 * @param price 价格文本（如"123.45"）
 * @param showCurrency 是否显示货币符号（如¥）
 * @param priceIndex 价格索引（-1表示处理所有价格，适用于多价格字符串）
 * @param style 价格样式配置（可通过DSL或PriceLabelStyle自定义）
 * @param modifier Compose修饰符
 *
 * 用法示例：
 * ```kotlin
 * PriceText(price = "123.45", style = priceLabelStyle { ... })
 * ```
 */
@Composable
fun PriceText(
    price: String,
    showCurrency: Boolean = true,
    priceIndex: Int = -1,
    style: PriceLabelStyle = PriceLabelStyle(),
    modifier: Modifier = Modifier
) {
    // 解析价格字符串为分段
    val segments = remember(price, showCurrency, priceIndex, style) {
        PriceParser.parseComplexPrice(
            priceText = price,
            priceIndex = priceIndex,
            showCurrency = showCurrency,
            style = style,
        )
    }
    // 渲染分段
    PriceLabel(
        segments = segments,
        style = style,
        modifier = modifier
    )
}

/**
 * 价格和规格标签组件 - 支持价格、规格、划线价的组合展示
 *
 * @param price 价格文本
 * @param spec 规格文本（如"/斤"），可选
 * @param oriPrice 划线价文本（如原价），可选
 * @param style 价格样式配置
 * @param modifier Compose修饰符
 *
 * 用法示例：
 * ```kotlin
 * PriceAndSpecLabel(price = "123.45", spec = "/斤", oriPrice = "199.99")
 * ```
 */
@Composable
fun PriceAndSpecLabel(
    price: String,
    spec: String? = null,
    oriPrice: String? = null,
    style: PriceLabelStyle = PriceLabelStyle(),
    modifier: Modifier = Modifier
) {
    // 处理掩码价格
    val processedPrice = remember(price, style.showMask) {
        if (style.showMask) PriceParser.formatPrice(price) else price
    }
    val processedOriPrice = remember(oriPrice, style.showMask) {
        if (style.showMask && oriPrice != null) PriceParser.formatPrice(oriPrice) else oriPrice
    }
    // 组装所有 segment
    val segments = remember(processedPrice, spec, processedOriPrice, style) {
        val priceSegments = PriceParser.parseComplexPrice(
            priceText = processedPrice,
            showCurrency = true,
            style = style,
        ).toMutableList()
        if (!spec.isNullOrEmpty()) {
            priceSegments.add(PriceSegment(PricePart.SLASH, "/", marginLeft = style.specStyle.marginLeft.takeIf { it > 0f } ?: PriceConfig.defaultSlashMarginLeft))
            priceSegments.add(PriceSegment(PricePart.SPEC, spec, marginLeft = 0f))
        }
        if (!processedOriPrice.isNullOrEmpty()) {
            priceSegments.add(PriceSegment(PricePart.LINE_PRICE, PriceParser.showCurrency(processedOriPrice), marginLeft = style.linePriceStyle.marginLeft.takeIf { it > 0f } ?: PriceConfig.defaultLinePriceMarginLeft))
        }
        priceSegments
    }
    // 渲染分段
    PriceLabel(
        segments = segments,
        style = style,
        modifier = modifier
    )
}

/**
 * 自定义价格标签组件 - 支持完全自定义的价格内容和样式分段
 *
 * @param items 自定义价格项列表（每项可配置内容、样式、是否为价格、保留小数位数等）
 * @param maxLines 最大行数，默认1
 * @param verticalAlignment 垂直对齐方式，默认底部对齐
 * @param modifier Compose修饰符
 *
 * 用法示例：
 * ```kotlin
 * CustomPriceLabel(items = listOf(
 *   CustomPriceItem(content = "¥", style = ...),
 *   CustomPriceItem(content = "123", style = ...),
 *   CustomPriceItem(content = ".45", style = ...)
 * ))
 * ```
 *
 * 功能：
 * - isPrice=true时，自动按价格模式智能分段，支持货币、整数、小数、单位等，应用item.style行为配置
 * - isPrice=false时，直接渲染为自定义文本分段
 * - 每段优先使用item.style自定义样式，否则用全局style
 */
@Composable
fun CustomPriceLabel(
    items: List<CustomPriceItem>,
    maxLines: Int = 1,
    verticalAlignment: Alignment.Vertical = Alignment.Bottom,
    modifier: Modifier = Modifier
) {
    // 通过PriceParser统一组装所有分段
    val segments = remember(items) {
        PriceParser.parseCustomSegments(items)
    }
    Row(modifier = modifier,
        verticalAlignment = verticalAlignment,
        horizontalArrangement = Arrangement.Start
    ) {
        segments.forEach { segment ->
            if (segment.marginLeft > 0f) {
                Spacer(modifier = Modifier.width(segment.marginLeft.dp))
            }
            val defaultStyle = PriceConfig.defaultStyle.toPriceStyle()
            val style = segment.customStyle?.toPriceStyle()?.let {
                TextStyle(
                    fontSize = it.fontSize,
                    color = it.color,
                    fontFamily = it.fontFamily,
                    fontWeight = it.fontWeight,
                    textDecoration = it.textDecoration
                )
            } ?: TextStyle(
                fontSize = defaultStyle.fontSize,
                color = defaultStyle.color,
                fontFamily = defaultStyle.fontFamily,
                fontWeight = defaultStyle.fontWeight,
                textDecoration = defaultStyle.textDecoration
            )

            BasicText(
                text = segment.content,
                style = style,
                maxLines = maxLines,
                overflow = TextOverflow.Ellipsis,
                modifier = if (verticalAlignment == Alignment.Bottom) Modifier.alignByBaseline() else Modifier
            )
        }
    }
}

/**
 * 价格分段渲染组件（内部使用）
 *
 * 根据配置决定使用自适应裁剪（AdaptPrice）还是基础渲染（BasicPrice）。
 *
 * @param segments 价格分段列表
 * @param style 价格样式配置
 * @param modifier Compose修饰符
 */
@Composable
private fun PriceLabel(
    segments: List<PriceSegment>,
    style: PriceLabelStyle,
    modifier: Modifier = Modifier,
) {
    // 若启用自动裁剪或自适应字号，且为单行，使用AdaptPrice，否则用BasicPrice
    if ((style.autoClip || style.autoFitSize) && style.maxLines == 1) {
        AdaptPrice(
            segments = segments,
            style = style,
            modifier = modifier
        )
    } else {
        BasicPrice(
            segments = segments,
            style = style,
            modifier = modifier
        )
    }
}

/**
 * 基础价格分段渲染（内部使用）
 *
 * 直接按分段顺序渲染所有内容，不做自动裁剪和缩放。
 * 支持多行、对齐、间距等。
 */
@Composable
private fun BasicPrice(
    segments: List<PriceSegment>,
    style: PriceLabelStyle,
    modifier: Modifier = Modifier,
) {
    // 如果设置了多行，使用AnnotatedString来保持不同段落的样式并支持换行
    if (style.maxLines > 1) {
        val textMeasurer = rememberTextMeasurer()
        val density = LocalDensity.current
        
        val annotatedString = buildAnnotatedString {
            segments.forEach { segment ->
                // 精确计算左边距的空格数量
                if (segment.marginLeft > 0f) {
                    val segmentStyle = PriceUtils.getPriceTextStyle(style, segment)
                    val spaceWidth = with(density) { segment.marginLeft.dp.toPx() }
                    val spaceChar = " "

                    // 使用标准小字体测量空格宽度，提高精度
                    val standardSpaceStyle = TextStyle(
                        fontSize = 12.sp,  // 使用标准小字体
                        fontFamily = segmentStyle.fontFamily,
                        fontWeight = segmentStyle.fontWeight
                    )
                    val spaceTextLayout = textMeasurer.measure(
                        text = AnnotatedString(spaceChar),
                        style = standardSpaceStyle
                    )
                    val singleSpaceWidth = spaceTextLayout.size.width

                    // 计算需要的空格数量
                    val spaceCount = if (singleSpaceWidth > 0) {
                        (spaceWidth / singleSpaceWidth).toInt().coerceAtLeast(1)
                    } else {
                        // 兜底：如果无法测量空格宽度，使用估算
                        (segment.marginLeft / 3f).toInt().coerceAtLeast(1)  // 调整兜底估算值
                    }

                    append(" ".repeat(spaceCount))
                }
                
                // 为每个段落应用对应的样式
                withStyle(PriceUtils.getPriceTextStyle(style, segment).toSpanStyle()) {
                    append(segment.content)
                }
            }
        }
        
        BasicText(
            text = annotatedString,
            maxLines = style.maxLines,
            overflow = TextOverflow.Ellipsis,
            modifier = modifier
        )
    } else {
        // 单行时保持原有的Row布局，支持精确的间距控制
        Row(
            modifier = modifier,
            verticalAlignment = if (style.verticalAlignment == PriceAlignment.BASELINE) Alignment.Bottom else style.verticalAlignment,
            horizontalArrangement = Arrangement.Start
        ) {
            segments.forEach { segment ->
                if (segment.marginLeft > 0f) {
                    Spacer(modifier = Modifier.width(segment.marginLeft.dp))
                }
                BasicText(
                    text = segment.content,
                    style = PriceUtils.getPriceTextStyle(style, segment),
                    maxLines = style.maxLines,
                    overflow = TextOverflow.Ellipsis,
                    modifier = if (style.verticalAlignment == PriceAlignment.BASELINE) Modifier.alignByBaseline() else Modifier
                )
            }
        }
    }
}

/**
 * 价格自适应处理（带优先级省略，内部使用）
 *
 * 用于电商场景下的价格展示，自动根据最大宽度裁剪内容，优先级如下：
 * 1. 隐藏划线价（如有）
 * 2. 隐藏单位（如有）
 * 3. 隐藏小数部分（如有）
 * 4. 只保留货币+整数，若仍超宽则对整数部分逐字裁剪并加省略号
 * 5. 兜底只显示货币+省略号
 *
 * 每一步裁剪后都会重新测量宽度，最大化展示内容，保证性能和体验。
 *
 * @param segments 价格分段列表
 * @param style 价格样式配置
 * @param modifier Compose修饰符
 *
 * 性能说明：
 * - 所有测量和裁剪逻辑均为局部变量，未用Compose状态，避免多余重组。
 * - 仅在内容或样式变化时重新计算，适合大部分电商场景。
 */
@Composable
private fun AdaptPrice(
    segments: List<PriceSegment>,
    style: PriceLabelStyle = PriceLabelStyle(),
    modifier: Modifier = Modifier
) {
    BoxWithConstraints(modifier) {
        val textMeasurer = rememberTextMeasurer()
        val density = LocalDensity.current
        val maxPx = with(density) { maxWidth.roundToPx() }
        var currentSegments = segments
        var fits = false
        var measuredWidth: Int
        var currentStyle = style
        // ====== autoFitSize 逻辑开始 ======
        if (style.autoFitSize) {
            // 记录原始字号
            val origFontSizes = mapOf(
                PricePart.CURRENCY to style.currencyStyle.fontSize.toFloat(),
                PricePart.INTEGER to style.integerStyle.fontSize.toFloat(),
                PricePart.DECIMAL to style.decimalStyle.fontSize.toFloat(),
                PricePart.SPEC to style.specStyle.fontSize.toFloat(),
                PricePart.OPERATOR to style.operatorStyle.fontSize.toFloat(),
                PricePart.LINE_PRICE to style.linePriceStyle.fontSize.toFloat(),
                PricePart.NOT_PRICE to style.notPriceStyle.fontSize.toFloat()
            )
            val minFontSize = style.minFontSizeSp.toFloat()
            measuredWidth = PriceUtils.measureSegmentsWidthAccurate(currentSegments, style, textMeasurer, density)
            if (measuredWidth > maxPx) {
                // 二分法缩放字号，找到最合适的缩放比例
                val minOrigFontSize = origFontSizes.values.minOrNull() ?: PriceConfig.defaultStyle.fontSize.toFloat()
                val maxScale = minFontSize / minOrigFontSize
                var low = maxScale
                var high = 1.0f
                var bestScale = 1.0f
                while (high - low > 0.01f) {
                    val mid = (low + high) / 2
                    val scaledStyle = PriceUtils.scaleLabelStyleFontSize(style, origFontSizes, mid)
                    val w = PriceUtils.measureSegmentsWidthAccurate(currentSegments, scaledStyle, textMeasurer, density)
                    if (w <= maxPx) {
                        bestScale = mid
                        low = mid
                    } else {
                        high = mid
                    }
                }
                if (bestScale < 1.0f) {
                    currentStyle = PriceUtils.scaleLabelStyleFontSize(style, origFontSizes, bestScale)
                    measuredWidth = PriceUtils.measureSegmentsWidthAccurate(currentSegments, currentStyle, textMeasurer, density)
                    if (measuredWidth <= maxPx) {
                        fits = true
                    }
                }
            } else {
                fits = true
            }
        }
        // ====== autoFitSize 逻辑结束 ======
        // 步骤1：裁剪划线价
        if (!fits) {
            val noLinePrice = currentSegments.filterNot { it.type == PricePart.LINE_PRICE }
            measuredWidth = PriceUtils.measureSegmentsWidthAccurate(noLinePrice, currentStyle, textMeasurer, density)
            if (measuredWidth <= maxPx) {
                currentSegments = noLinePrice
                fits = true
            } else {
                // 步骤2：裁剪单位
                val noUnit = noLinePrice.filterNot { it.type == PricePart.SPEC || it.type == PricePart.SLASH }
                measuredWidth = PriceUtils.measureSegmentsWidthAccurate(noUnit, currentStyle, textMeasurer, density)
                if (measuredWidth <= maxPx) {
                    currentSegments = noUnit
                    fits = true
                } else {
                    // 步骤3：只保留货币+整数+小数（含小数点）
                    val onlyMain = noUnit.filter { 
                        it.type == PricePart.CURRENCY || 
                        it.type == PricePart.INTEGER || 
                        it.type == PricePart.POINT || 
                        it.type == PricePart.DECIMAL 
                    }
                    measuredWidth = PriceUtils.measureSegmentsWidthAccurate(onlyMain, currentStyle, textMeasurer, density)
                    if (measuredWidth <= maxPx) {
                        currentSegments = onlyMain
                        fits = true
                    } else {
                        // 步骤4：只保留货币+整数，逐字裁剪整数
                        val currencySegment = onlyMain.find { it.type == PricePart.CURRENCY }
                        val integerSegment = onlyMain.find { it.type == PricePart.INTEGER }
                        val prefixSegments = onlyMain.filter { it.type == PricePart.CURRENCY }
                        val intStr = integerSegment?.content ?: ""
                        val onlyCurrencyAndInt = prefixSegments + PriceSegment(PricePart.INTEGER, intStr)
                        if (PriceUtils.measureSegmentsWidthAccurate(onlyCurrencyAndInt, currentStyle, textMeasurer, density) <= maxPx) {
                            currentSegments = onlyCurrencyAndInt
                            fits = true
                        } else {
                            var found = false
                            for (i in intStr.length - 1 downTo 1) {
                                val testSegments = prefixSegments + PriceSegment(PricePart.INTEGER, intStr.take(i) + "…")
                                if (PriceUtils.measureSegmentsWidthAccurate(testSegments, currentStyle, textMeasurer, density) <= maxPx) {
                                    currentSegments = testSegments
                                    found = true
                                    break
                                }
                            }
                            if (!found) {
                                // 步骤5：兜底，只显示货币+省略号
                                val currency = currencySegment?.content ?: ""
                                currentSegments = listOf(
                                    PriceSegment(PricePart.CURRENCY, currency),
                                    PriceSegment(PricePart.NOT_PRICE, "…")
                                )
                            }
                        }
                    }
                }
            }
        }
        // 渲染 currentSegments
        Row(
            modifier = Modifier,
            horizontalArrangement = Arrangement.Start
        ) {
            currentSegments.forEach { segment ->
                if (segment.marginLeft > 0f) {
                    Box(Modifier.alignByBaseline()) {
                        Spacer(Modifier.width(segment.marginLeft.dp))
                    }
                }
                BasicText(
                    text = segment.content,
                    maxLines = 1,
                    style = PriceUtils.getPriceTextStyle(currentStyle, segment),
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.alignByBaseline()
                )
            }
        }
    }
}

/**
 * 设置自适应大小 - 对应Android原生PriceView.setAutoFitSize()
 */
fun PriceLabelStyle.withAutoFitSize(autoFitSize: Boolean): PriceLabelStyle {
    return this.copy(autoFitSize = autoFitSize)
}

/**
 * 设置自动裁剪 - 对应Android原生PriceView.setAutoClip()
 */
fun PriceLabelStyle.withAutoClip(autoClip: Boolean): PriceLabelStyle {
    return this.copy(autoClip = autoClip)
}
/**
 * 设置垂直对齐方式 - 解决不同字体大小的对齐问题
 */
fun PriceLabelStyle.withVerticalAlignment(alignment: Alignment.Vertical): PriceLabelStyle {
    return this.copy(verticalAlignment = alignment)
}

/**
 * 快速设置每个部分的字体大小
 */
fun PriceLabelStyle.withCustomFontSize(
    currencyFontSize: Int? = null,
    integerFontSize: Int? = null,
    decimalFontSize: Int? = null,
    unitFontSize: Int? = null,
    linePriceFontSize: Int? = null): PriceLabelStyle {
    return this.copy(
        currencyStyle = currencyFontSize?.let { currencyStyle.copy(fontSize = it) } ?: currencyStyle,
        integerStyle = integerFontSize?.let { integerStyle.copy(fontSize = it) } ?: integerStyle,
        decimalStyle = decimalFontSize?.let { decimalStyle.copy(fontSize = it) } ?: decimalStyle,
        specStyle = unitFontSize?.let { specStyle.copy(fontSize = it) } ?: specStyle,
        linePriceStyle = linePriceFontSize?.let { linePriceStyle.copy(fontSize = it) } ?: linePriceStyle
    )
}