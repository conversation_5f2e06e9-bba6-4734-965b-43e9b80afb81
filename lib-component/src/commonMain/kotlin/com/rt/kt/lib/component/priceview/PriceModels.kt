package com.rt.kt.lib.component.priceview


/**
 * @ClassName: PriceModels
 * @Description:价格组件的数据模型
 * @Date: 2025/5/28
 */

/**
 * 价格部分类型
 */
internal enum class PricePart {
    CURRENCY,    // 货币符号 ¥
    INTEGER,     // 整数部分
    DECIMAL,     // 小数部分
    POINT,       // 小数点
    OPERATOR,    // 操作符 +, -
    LINE_PRICE,  // 划线价
    SLASH,       // 斜杠 /
    SPEC,        // 规格单位部分 /个
    STAR,        // 星号 *
    CUSTOM,      // 自定义样式
    NOT_PRICE    // 非价格文本
}

/**
 * 价格段
 */
internal data class PriceSegment(
    val type: PricePart,
    val content: String,
    val marginLeft: Float = 0f,
    val canLeaveOut: Boolean = false, // 是否可省略
    val showStrikeThrough: Boolean = false, // 是否显示删除线
    val customStyle: PriceStyleConfig? = null // 可选自定义样式
)
