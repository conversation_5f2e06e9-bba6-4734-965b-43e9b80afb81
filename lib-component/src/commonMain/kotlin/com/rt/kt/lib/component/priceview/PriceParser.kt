package com.rt.kt.lib.component.priceview

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 价格解析器 （边解析边分段，无中间数据结构）
 *
 * 主要职责：
 * - 解析价格字符串为分段（如货币、整数、小数、单位、划线价等）
 * - 支持自定义分段与样式
 * - 提供格式化、补货币符号等辅助方法
 *
 * 用法：仅供库内部调用
 */
internal object PriceParser {

    // 价格正则表达式 - 匹配货币金额、掩码、星号等，支持单位（如/500g）
    private val priceRegex = Regex("([+-]?[￥¥]?(((0|([1-9]\\d*))\\.\\d+)|([1-9]\\d*)|0)(/[^\t\r\n ]*)?)|([￥¥]\\*+)")

    /**
     * 获取指定分段类型的左侧间距（dp）
     * 优先取样式配置，否则用全局默认
     * @param part 分段类型
     * @param style 当前价格标签样式
     * @return 间距（dp）
     */
    private fun getMargin(part: PricePart, style: PriceLabelStyle): Float {
        val styleMargin = when (part) {
            PricePart.CURRENCY -> style.currencyStyle.marginLeft.takeIf { it > 0 }
                ?: PriceConfig.defaultCurrencyMarginLeft
            PricePart.INTEGER -> style.integerStyle.marginLeft
            PricePart.DECIMAL -> style.decimalStyle.marginLeft
            PricePart.SPEC -> style.specStyle.marginLeft
            PricePart.OPERATOR -> style.operatorStyle.marginLeft.takeIf { it > 0 }
                ?: PriceConfig.defaultOperatorMarginLeft
            PricePart.LINE_PRICE -> style.linePriceStyle.marginLeft.takeIf { it > 0 }
                ?: PriceConfig.defaultLinePriceMarginLeft
            PricePart.NOT_PRICE -> style.notPriceStyle.marginLeft
            PricePart.SLASH -> PriceConfig.defaultSlashMarginLeft
            PricePart.POINT, PricePart.STAR, PricePart.CUSTOM -> 0f
        }
       return styleMargin
    }

    /**
     * 解析复杂价格字符串，返回分段结果，支持分段 margin 配置
     *
     * @param priceText 价格字符串（如"¥123.45/斤"）
     * @param priceIndex 价格索引（-1表示全部处理，适用于多价格场景）
     * @param showCurrency 是否显示货币符号
     * @param style 价格标签样式配置
     * @return 分段列表（每段为PriceSegment）
     */
    fun parseComplexPrice(
        priceText: String,
        priceIndex: Int = -1,
        showCurrency: Boolean = true,
        style: PriceLabelStyle = PriceLabelStyle()
    ): List<PriceSegment> {
        if (priceText.isEmpty()) {
            return emptyList()
        }
        val segments = mutableListOf<PriceSegment>()
        val matcher = priceRegex.findAll(priceText)
        var lastEnd = 0
        var position = 0
        var foundSlash = false
        for (match in matcher) {
            if (foundSlash) break
            if (match.range.first > lastEnd) {
                val notPriceText = priceText.substring(lastEnd, match.range.first)
                foundSlash = handleSlashAndNotPrice(
                    notPriceText,
                    segments,
                    style
                )
                if (foundSlash) break
            }
            if (priceIndex == -1 || position == priceIndex) {
                addPriceSegments(
                    match.value,
                    segments,
                    showCurrency = showCurrency,
                    style = style
                )
            } else {
                addNotPriceSegments(match.value, segments, style)
            }
            lastEnd = match.range.last + 1
            position++
        }
        if (!foundSlash && lastEnd < priceText.length) {
            val remainingText = priceText.substring(lastEnd)
            handleSlashAndNotPrice(remainingText, segments, style)
        }
        return segments
    }

    /**
     * 边解析边分段，直接生成PriceSegment，支持分段 margin 配置
     *
     * @param priceText 单个价格片段（如"¥123.45"）
     * @param segments 分段结果累加列表
     * @param showCurrency 是否显示货币符号
     * @param style 当前样式
     */
    private fun addPriceSegments(
        priceText: String,
        segments: MutableList<PriceSegment>,
        showCurrency: Boolean,
        style: PriceLabelStyle = PriceLabelStyle()
    ) {
        val keepDigits = style.keepDigits
        val isNeedDeleteZeroOfSuffix = style.isNeedDeleteZeroOfSuffix
        val isPointSizeUseInteger = style.isPointSizeUseInteger
        var i = 0
        val len = priceText.length
        var hasCurrency = false
        var hasOperator = false
        var inDecimal = false
        var inSpec = false
        val integerPart = StringBuilder()
        val decimalPart = StringBuilder()
        val specPart = StringBuilder()
        // 处理正负号
        if (i < len && (priceText[i] == '+' || priceText[i] == '-')) {
            segments.add(PriceSegment(
                type = PricePart.OPERATOR,
                content = priceText[i].toString(),
                marginLeft = getMargin(PricePart.OPERATOR, style)
            ))
            i++
            hasOperator = true
        }
        // 处理货币符号
        if (i < len && (priceText[i] == '¥' || priceText[i] == '￥')) {
            segments.add(PriceSegment(
                type = PricePart.CURRENCY,
                content = priceText[i].toString(),
                marginLeft = getMargin(PricePart.CURRENCY, style),
            ))
            i++
            hasCurrency = true
        } else if (showCurrency) {
            segments.add(PriceSegment(
                type = PricePart.CURRENCY,
                content = PriceConfig.defaultCurrency,
                marginLeft = getMargin(PricePart.CURRENCY, style),
            ))
            hasCurrency = true
        }
        // 主体部分解析
        while (i < len) {
            val c = priceText[i]
            when {
                c == '.' -> {
                    if (integerPart.isNotEmpty()) {
                        segments.add(PriceSegment(
                            type = PricePart.INTEGER,
                            content = integerPart.toString(),
                            marginLeft = getMargin(PricePart.INTEGER, style),
                        ))
                        integerPart.clear()
                    }
                    segments.add(PriceSegment(
                        type = if (isPointSizeUseInteger) PricePart.INTEGER else PricePart.DECIMAL,
                        content = ".",
                        marginLeft = getMargin(if (isPointSizeUseInteger) PricePart.INTEGER else PricePart.DECIMAL, style),
                    ))
                    inDecimal = true
                }
                c == '/' -> {
                    if (inDecimal && decimalPart.isNotEmpty()) {
                        val processedDecimal = processDecimal(decimalPart.toString(), keepDigits, isNeedDeleteZeroOfSuffix)
                        if (processedDecimal.isNotEmpty()) {
                            segments.add(PriceSegment(
                                type = PricePart.DECIMAL,
                                content = processedDecimal,
                                marginLeft = getMargin(PricePart.DECIMAL, style),
                            ))
                        }
                        decimalPart.clear()
                        inDecimal = false
                    } else if (integerPart.isNotEmpty()) {
                        segments.add(PriceSegment(
                            type = PricePart.INTEGER,
                            content = integerPart.toString(),
                            marginLeft = getMargin(PricePart.INTEGER, style),
                        ))
                        integerPart.clear()
                    }
                    segments.add(PriceSegment(
                        type = PricePart.SLASH,
                        content = "/",
                        marginLeft = getMargin(PricePart.SPEC, style),
                    ))
                    inSpec = true
                }
                c == '*' -> {
                    if (inDecimal && decimalPart.isNotEmpty()) {
                        val processedDecimal = processDecimal(decimalPart.toString(), keepDigits, isNeedDeleteZeroOfSuffix)
                        if (processedDecimal.isNotEmpty()) {
                            segments.add(PriceSegment(
                                type = PricePart.DECIMAL,
                                content = processedDecimal,
                                marginLeft = getMargin(PricePart.DECIMAL, style),
                            ))
                        }
                        decimalPart.clear()
                        inDecimal = false
                    } else if (integerPart.isNotEmpty()) {
                        segments.add(PriceSegment(
                            type = PricePart.INTEGER,
                            content = integerPart.toString(),
                            marginLeft = getMargin(PricePart.INTEGER, style),
                        ))
                        integerPart.clear()
                    }
                    segments.add(PriceSegment(
                        type = PricePart.STAR,
                        content = "*",
                        marginLeft = getMargin(PricePart.OPERATOR, style),
                    ))
                }
                inSpec -> {
                    specPart.append(c)
                }
                inDecimal -> {
                    decimalPart.append(c)
                }
                c in '0'..'9' -> {
                    if (inDecimal) {
                        decimalPart.append(c)
                    } else if (!inSpec) {
                        integerPart.append(c)
                    } else {
                        specPart.append(c)
                    }
                }
                else -> {
                    if (inSpec) {
                        specPart.append(c)
                    }
                }
            }
            i++
        }
        // 收尾处理
        if (specPart.isNotEmpty()) {
            segments.add(PriceSegment(
                type = PricePart.SPEC,
                content = specPart.toString(),
                marginLeft = getMargin(PricePart.SPEC, style),
            ))
        } else if (decimalPart.isNotEmpty()) {
            val processedDecimal = processDecimal(decimalPart.toString(), keepDigits, isNeedDeleteZeroOfSuffix)
            if (processedDecimal.isNotEmpty()) {
                segments.add(PriceSegment(
                    type = PricePart.DECIMAL,
                    content = processedDecimal,
                    marginLeft = getMargin(PricePart.DECIMAL, style),
                ))
            }
        } else if (integerPart.isNotEmpty()) {
            segments.add(PriceSegment(
                type = PricePart.INTEGER,
                content = integerPart.toString(),
                marginLeft = getMargin(PricePart.INTEGER, style),
            ))
            // 新增：如果keepDigits>0，且没有小数点，自动补小数分段
            if (keepDigits > 0) {
                segments.add(PriceSegment(
                    type = PricePart.DECIMAL,
                    content = "." + "0".repeat(keepDigits),
                    marginLeft = getMargin(PricePart.DECIMAL, style),
                ))
            }
        }

        // 后处理：检查并移除孤立的小数点（没有对应小数部分的小数点）
        val hasDecimalPoint = segments.any { it.content == "." && (it.type == PricePart.DECIMAL || it.type == PricePart.INTEGER) }
        if (hasDecimalPoint) {
            val filteredSegments = mutableListOf<PriceSegment>()
            for (i in segments.indices) {
                val segment = segments[i]
                // 如果当前是小数点，检查下一个是否为有效的小数部分
                if (segment.content == "." && (segment.type == PricePart.DECIMAL || segment.type == PricePart.INTEGER)) {
                    val nextSegment = if (i + 1 < segments.size) segments[i + 1] else null
                    // 只有当下一个是非空的小数部分时，才保留小数点
                    if (nextSegment?.type == PricePart.DECIMAL && nextSegment.content.isNotEmpty()) {
                        filteredSegments.add(segment)
                    }
                    // 否则跳过这个小数点
                } else {
                    filteredSegments.add(segment)
                }
            }
            segments.clear()
            segments.addAll(filteredSegments)
        }
    }

    /**
     * 添加非价格段（如普通文本、分隔符等）
     * @param text 非价格内容
     * @param segments 分段结果累加列表
     * @param style 当前样式
     */
    private fun addNotPriceSegments(text: String, segments: MutableList<PriceSegment>, style: PriceLabelStyle) {
        if (text.isNotEmpty()) {
            segments.add(PriceSegment(
                type = PricePart.NOT_PRICE,
                content = text,
                marginLeft = getMargin(PricePart.NOT_PRICE, style),
            ))
        }
    }

    /**
     * 处理斜杠及其后内容整体作为 NOT_PRICE 的逻辑
     * 用于兼容如“/件”这种单位整体处理
     * @return 是否已处理斜杠（true则后续不再处理）
     */
    private fun handleSlashAndNotPrice(
        text: String,
        segments: MutableList<PriceSegment>,
        style: PriceLabelStyle
    ): Boolean {
        if (text.contains("/")) {
            val slashIndex = text.indexOf("/")
            if (slashIndex > 0) {
                addNotPriceSegments(text.substring(0, slashIndex), segments, style)
            }
            addNotPriceSegments("/", segments, style)
            val afterSlash = text.substring(slashIndex + 1)
            if (afterSlash.isNotEmpty()) {
                addNotPriceSegments(afterSlash, segments, style)
            }
            return true
        } else {
            addNotPriceSegments(text, segments, style)
            return false
        }
    }

    /**
     * 处理小数部分，保留指定位数，支持删除末尾零
     * @param decimal 小数部分字符串
     * @param keepDigits 保留小数位数
     * @param isNeedDeleteZeroOfSuffix 是否删除末尾零
     * @return 处理后的小数字符串
     */
    private fun processDecimal(decimal: String, keepDigits: Int, isNeedDeleteZeroOfSuffix: Boolean): String {
        if (decimal.isEmpty()) return ""
        var result = when {
            keepDigits == 0 -> ""
            keepDigits > 0 -> decimal.take(keepDigits).padEnd(keepDigits, '0')
            else -> decimal
        }
        if (isNeedDeleteZeroOfSuffix) {
            result = result.trimEnd('0')
            if (result.endsWith(".")) {
                result = result.dropLast(1)
            }
        }
        return result
    }

    /**
     * 格式化价格（如掩码处理：-1 => ¥***）
     * @param price 原始价格字符串
     * @return 格式化后字符串
     */
    fun formatPrice(price: String?): String {
        if (price.isNullOrEmpty()) return ""
        // 处理掩码价格
        if (price.startsWith("-1")) {
            return price.replaceFirst("-1", "${PriceConfig.defaultCurrency}***")
        }
        return price
    }

    /**
     * 显示货币符号（如自动补全¥）
     * @param price 原始价格字符串
     * @return 带货币符号的字符串
     */
    fun showCurrency(price: String?): String {
        if (price.isNullOrEmpty()) return ""
        val currency = PriceConfig.defaultCurrency
        val altCurrency = PriceConfig.alternateCurrency
        return if (!price.startsWith(currency) && !price.startsWith(altCurrency)) {
            "$currency$price"
        } else {
            price
        }
    }

    /**
     * 支持自定义分段与样式
     *
     * @param items 自定义价格项列表（每项可配置内容、样式、是否为价格、保留小数位数等）
     * @return 分段列表（每段为PriceSegment）
     *
     * 功能：
     * - isPrice=true时，自动按价格模式智能分段，支持货币、整数、小数、单位等，应用item.style行为配置
     * - isPrice=false时，直接渲染为自定义文本分段
     * - 每段优先使用item.style自定义样式
     */
    fun parseCustomSegments(items: List<CustomPriceItem>): List<PriceSegment> {
        return items.flatMap { item ->
            if (item.isPrice) {
                // 价格模式，智能分段，应用item.style的行为配置
                val style = PriceLabelStyle(
                    integerStyle = item.style,
                    decimalStyle = item.style,
                    currencyStyle = item.style,
                    specStyle = item.style,
                    operatorStyle = item.style,
                    linePriceStyle = item.style,
                    notPriceStyle = item.style,
                    isNeedDeleteZeroOfSuffix = item.isNeedDeleteZeroOfSuffix,
                    keepDigits = item.keepDigits,
                    isPointSizeUseInteger = item.isPointSizeUseInteger,
                )
                parseComplexPrice(
                    priceText = item.content,
                    showCurrency = item.showCurrency,
                    style = style
                ).map { seg ->
                    seg.copy(
                        canLeaveOut = item.canLeaveOut,
                        showStrikeThrough = item.style.strikeThrough,
                        customStyle = item.style
                    )
                }
            } else {
                // 非价格，直接渲染
                listOf(
                    PriceSegment(
                        type = PricePart.CUSTOM,
                        content = item.content,
                        marginLeft = item.style.marginLeft,
                        canLeaveOut = item.canLeaveOut,
                        showStrikeThrough = item.style.strikeThrough,
                        customStyle = item.style
                    )
                )
            }
        }
    }
}

/**
 * 价格工具类，提供样式转换、宽度测量、缩放等辅助方法
 *
 * 用法：仅供库内部调用
 */
internal object PriceUtils {
    /**
     * 获取指定分段的TextStyle
     * @param style 当前价格标签样式
     * @param segment 分段信息
     * @return Compose TextStyle
     */
    fun getPriceTextStyle(style: PriceLabelStyle = PriceLabelStyle(), segment: PriceSegment): TextStyle {
        return TextStyle(
            fontSize = when (segment.type) {
                PricePart.CURRENCY -> style.currencyStyle.toPriceStyle().fontSize
                PricePart.INTEGER -> style.integerStyle.toPriceStyle().fontSize
                PricePart.DECIMAL -> style.decimalStyle.toPriceStyle().fontSize
                PricePart.SLASH, PricePart.SPEC -> style.specStyle.toPriceStyle().fontSize
                PricePart.OPERATOR -> style.operatorStyle.toPriceStyle().fontSize
                PricePart.LINE_PRICE -> style.linePriceStyle.toPriceStyle().fontSize
                else -> style.notPriceStyle.toPriceStyle().fontSize
            },
            color = when (segment.type) {
                PricePart.CURRENCY -> style.currencyStyle.toPriceStyle().color
                PricePart.INTEGER -> style.integerStyle.toPriceStyle().color
                PricePart.DECIMAL -> style.decimalStyle.toPriceStyle().color
                PricePart.SLASH, PricePart.SPEC -> style.specStyle.toPriceStyle().color
                PricePart.OPERATOR -> style.operatorStyle.toPriceStyle().color
                PricePart.LINE_PRICE -> style.linePriceStyle.toPriceStyle().color
                else -> style.notPriceStyle.toPriceStyle().color
            },
            fontFamily = when (segment.type) {
                PricePart.CURRENCY -> style.currencyStyle.toPriceStyle().fontFamily
                PricePart.INTEGER -> style.integerStyle.toPriceStyle().fontFamily
                PricePart.DECIMAL -> style.decimalStyle.toPriceStyle().fontFamily
                PricePart.SLASH, PricePart.SPEC -> style.specStyle.toPriceStyle().fontFamily
                PricePart.OPERATOR -> style.operatorStyle.toPriceStyle().fontFamily
                PricePart.LINE_PRICE -> style.linePriceStyle.toPriceStyle().fontFamily
                else -> style.notPriceStyle.toPriceStyle().fontFamily
            },
            fontWeight = when (segment.type) {
                PricePart.CURRENCY -> style.currencyStyle.toPriceStyle().fontWeight
                PricePart.INTEGER -> style.integerStyle.toPriceStyle().fontWeight
                PricePart.DECIMAL -> style.decimalStyle.toPriceStyle().fontWeight
                PricePart.SLASH, PricePart.SPEC -> style.specStyle.toPriceStyle().fontWeight
                PricePart.OPERATOR -> style.operatorStyle.toPriceStyle().fontWeight
                PricePart.LINE_PRICE -> style.linePriceStyle.toPriceStyle().fontWeight
                else -> style.notPriceStyle.toPriceStyle().fontWeight
            },
            textDecoration = when (segment.type) {
                PricePart.LINE_PRICE -> TextDecoration.LineThrough
                else -> null
            }
        )
    }

    /**
     * 精确测量所有分段的总宽度（px）
     * @param segments 分段列表
     * @param style 当前样式
     * @param textMeasurer Compose文本测量器
     * @param density 当前密度
     * @return 总宽度（px）
     */
    fun measureSegmentsWidthAccurate(
        segments: List<PriceSegment>,
        style: PriceLabelStyle,
        textMeasurer: TextMeasurer,
        density: Density
    ): Int {
        return segments.sumOf { segment ->
            val priceStyle = when (segment.type) {
                PricePart.CURRENCY -> style.currencyStyle.toPriceStyle()
                PricePart.INTEGER -> style.integerStyle.toPriceStyle()
                PricePart.DECIMAL -> style.decimalStyle.toPriceStyle()
                PricePart.SPEC -> style.specStyle.toPriceStyle()
                PricePart.OPERATOR -> style.operatorStyle.toPriceStyle()
                PricePart.LINE_PRICE -> style.linePriceStyle.toPriceStyle()
                else -> style.notPriceStyle.toPriceStyle()
            }
            val textStyle = TextStyle(
                fontSize = priceStyle.fontSize,
                color = priceStyle.color,
                fontFamily = priceStyle.fontFamily,
                fontWeight = priceStyle.fontWeight
            )
            val textLayoutResult = textMeasurer.measure(
                text = AnnotatedString(segment.content),
                style = textStyle
            )
            val marginPx = with(density) { segment.marginLeft.dp.roundToPx() }
            textLayoutResult.size.width + marginPx
        }
    }

    /**
     * 按比例缩放所有分段的字号
     * @param style 当前样式
     * @param origFontSizes 原始字号map
     * @param scale 缩放比例（0~1）
     * @return 新的PriceLabelStyle
     */
    fun scaleLabelStyleFontSize(style: PriceLabelStyle, origFontSizes: Map<PricePart, Float>, scale: Float): PriceLabelStyle {
        val defaultFontSize = PriceConfig.defaultStyle.fontSize.toFloat()
        return style.copy(
            currencyStyle = style.currencyStyle.copy(fontSize = (origFontSizes.getOrElse(PricePart.CURRENCY) { defaultFontSize } * scale).toInt()),
            integerStyle = style.integerStyle.copy(fontSize = (origFontSizes.getOrElse(PricePart.INTEGER) { defaultFontSize } * scale).toInt()),
            decimalStyle = style.decimalStyle.copy(fontSize = (origFontSizes.getOrElse(PricePart.DECIMAL) { defaultFontSize } * scale).toInt()),
            specStyle = style.specStyle.copy(fontSize = (origFontSizes.getOrElse(PricePart.SPEC) { defaultFontSize } * scale).toInt()),
            operatorStyle = style.operatorStyle.copy(fontSize = (origFontSizes.getOrElse(PricePart.OPERATOR) { defaultFontSize } * scale).toInt()),
            linePriceStyle = style.linePriceStyle.copy(fontSize = (origFontSizes.getOrElse(PricePart.LINE_PRICE) { defaultFontSize } * scale).toInt()),
            notPriceStyle = style.notPriceStyle.copy(fontSize = (origFontSizes.getOrElse(PricePart.NOT_PRICE) { defaultFontSize } * scale).toInt())
        )
    }
}

/**
 * PriceStyle 仅供库内部渲染使用
 */
internal data class PriceStyle(
    val fontSize: TextUnit,
    val color: Color,
    val fontFamily: FontFamily,
    val fontWeight: FontWeight,
    val textDecoration: TextDecoration?,
    val marginLeft: Float
)

/**
 * 将PriceStyleConfig转换为PriceStyle
 * 支持全局配置和单独配置的灵活组合
 * 当textStyle为REGULAR/MEDIUM/BOLD时，fontWeight不生效
 */
internal fun PriceStyleConfig.toPriceStyle(): PriceStyle {
    return PriceStyle(
        fontSize = this.fontSize.sp,
        color = this.color,
        fontFamily = this.customFontFamily ?: PriceConfig.priceFontFamily ?: FontFamily.Default,
        fontWeight = this.fontWeight,
        textDecoration = if (this.strikeThrough) TextDecoration.LineThrough else null,
        marginLeft = this.marginLeft
    )
}
