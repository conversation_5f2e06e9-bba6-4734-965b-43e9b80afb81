package com.rt.kt.lib.component.priceview

import androidx.compose.ui.Alignment
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.graphics.Color

/**
 * @ClassName: PriceConfig
 * @Description: 价格组件全局配置管理
 * @Date: 2025/5/28
 */

/**
 * 价格组件全局配置
 */
object PriceConfig {
    var priceFontFamily: FontFamily? = null

    // 统一默认样式配置
    var defaultStyle: PriceStyleConfig = PriceStyleConfig(
        fontSize = 16,
        color = Color(0xFF000000),
        fontWeight = FontWeight.Normal
    )

    // 间距配置 (dp)
    var defaultSlashMarginLeft: Float = 4.0F
    var defaultLinePriceMarginLeft: Float = 8.0F
    var defaultOperatorMarginLeft: Float = 2.0F
    var defaultCurrencyMarginLeft: Float = 1.0F

    // 货币符号配置
    var defaultCurrency: String = "¥"
    var alternateCurrency: String = "￥"

    // 其他符号配置
    var plusSymbol: String = "+"
    var minusSymbol: String = "-"
    var pointSymbol: String = "."
    var starSymbol: String = "*"
    var slashSymbol: String = "/"
    var ellipsisSymbol: String = "…"

    // 对齐配置
    var defaultAlignment: Alignment.Vertical = PriceAlignment.BASELINE

    /**
     * 更新默认样式配置
     */
    fun updateDefaultStyle(style: PriceStyleConfig) {
        defaultStyle = style
    }
}

//fun PriceStyleConfig.resolveFontFamily(): FontFamily {
//    customFontFamily?.let { return it }
//    PriceConfig.priceFontFamily?.let { global ->
//        return when (fontWeight) {
//            FontWeight.Bold -> global.bold
//            FontWeight.Medium, FontWeight.SemiBold -> global.medium
//            else -> global.normal
//        }
//    }
//    return FontFamily.Default
//}
//
//data class PriceFontFamily(
//    val normal: FontFamily,
//    val medium: FontFamily,
//    val bold: FontFamily
//)

/**
 * 价格对齐方式 - 解决不同字体大小的对齐问题
 */
object PriceAlignment {
    val TOP = Alignment.Top           // 顶部对齐
    val CENTER = Alignment.CenterVertically  // 居中对齐
    val BOTTOM = Alignment.Bottom     // 底部对齐
    val BASELINE = Alignment.Bottom   // 基线对齐（推荐，类似Android原生baseline对齐）
}
