package com.rt.kt.lib.component.priceview

import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.graphics.Color

/**
 * @ClassName: PriceStyle
 * @Description: 价格样式配置和转换
 * @Date: 2025/5/30
 */

/**
 * PriceStyleConfig 供外部配置，支持所有可定制项。
 * 通过 toPriceStyle() 转为内部渲染模型。
 */
data class PriceStyleConfig(
    val fontSize: Int = 14,
    val color: Color = Color(0xFF000000),
    val fontWeight: FontWeight = FontWeight.Normal,
    val customFontFamily: FontFamily? = null,
    val strikeThrough: Boolean = false,
    val marginLeft: Float = 0f
)

/**
 * 价格标签样式配置，对外暴露，所有分部均为 PriceStyleConfig 类型。
 */
data class PriceLabelStyle(
    val integerStyle: PriceStyleConfig = PriceConfig.defaultStyle,
    val decimalStyle: PriceStyleConfig = PriceConfig.defaultStyle,
    val currencyStyle: PriceStyleConfig = PriceConfig.defaultStyle,
    val specStyle: PriceStyleConfig = PriceConfig.defaultStyle,
    val operatorStyle: PriceStyleConfig = PriceConfig.defaultStyle,
    val linePriceStyle: PriceStyleConfig = PriceConfig.defaultStyle,
    val notPriceStyle: PriceStyleConfig = PriceConfig.defaultStyle,

    // 行为配置
    val isNeedDeleteZeroOfSuffix: Boolean = false,  // 删除末尾零
    val keepDigits: Int = -1,                       // 保留小数位数
    val isPointSizeUseInteger: Boolean = true,      // 小数点字号跟随整数/小数，默认true

    val autoFitSize: Boolean = false,               // 自适应字体大小（根据最大宽度自动缩放字号，优先于裁剪内容
    val minFontSizeSp: Int = 12,                    // 新增：最小允许缩放到的字号（sp），默认12sp
    val autoClip: Boolean = false,                  // 自动裁剪
    val showMask: Boolean = false,                  // 显示掩码

    // 布局配置
    val maxLines: Int = 1,                          // 最大行数
    // 对齐配置 - 解决不同字体大小的对齐问题
    val verticalAlignment: Alignment.Vertical = PriceConfig.defaultAlignment  // 垂直对齐方式
)

// ================= DSL 构建器实现 =================
class PriceLabelStyleBuilder {
    var integerStyle: PriceStyleConfig = PriceConfig.defaultStyle
    var decimalStyle: PriceStyleConfig = PriceConfig.defaultStyle
    var currencyStyle: PriceStyleConfig = PriceConfig.defaultStyle
    var specStyle: PriceStyleConfig = PriceConfig.defaultStyle
    var operatorStyle: PriceStyleConfig = PriceConfig.defaultStyle
    var linePriceStyle: PriceStyleConfig = PriceConfig.defaultStyle
    var notPriceStyle: PriceStyleConfig = PriceConfig.defaultStyle

    // 行为配置
    var isNeedDeleteZeroOfSuffix: Boolean = false
    var keepDigits: Int = -1
    var autoFitSize: Boolean = false
    var minFontSizeSp: Int = 12
    var autoClip: Boolean = false
    var showMask: Boolean = false
    var maxLines: Int = 1
    var verticalAlignment: Alignment.Vertical = PriceConfig.defaultAlignment

    fun build() = PriceLabelStyle(
        integerStyle = integerStyle,
        decimalStyle = decimalStyle,
        currencyStyle = currencyStyle,
        specStyle = specStyle,
        operatorStyle = operatorStyle,
        linePriceStyle = linePriceStyle,
        notPriceStyle = notPriceStyle,
        isNeedDeleteZeroOfSuffix = isNeedDeleteZeroOfSuffix,
        keepDigits = keepDigits,
        autoFitSize = autoFitSize,
        minFontSizeSp = minFontSizeSp,
        autoClip = autoClip,
        showMask = showMask,
        maxLines = maxLines,
        verticalAlignment = verticalAlignment
    )
}

/**
 * DSL风格的PriceLabelStyle配置
 *
 * 用法示例：
 * val style = priceLabelStyle {
 *     integerStyle = PriceStyleConfig(fontSize = 20)
 *     decimalStyle = PriceStyleConfig(fontSize = 16)
 *     autoFitSize = true
 *     // ...
 * }
 */
fun priceLabelStyle(block: PriceLabelStyleBuilder.() -> Unit): PriceLabelStyle {
    return PriceLabelStyleBuilder().apply(block).build()
}

/**
 * 自定义价格项
 */
data class CustomPriceItem(
    val content: String,
    val style: PriceStyleConfig = PriceStyleConfig(),
    val canLeaveOut: Boolean = false, // 是否可以省略

    val isPrice: Boolean = false,     // 是否为价格
    val keepDigits: Int = -1,         // 保留小数位数
    val isNeedDeleteZeroOfSuffix: Boolean = false,  // 删除末尾零
    val showCurrency: Boolean = false,   // 显示货币符号
    val priceIndex: Int = -1,        // 价格索引
    val isPointSizeUseInteger: Boolean = true, // 小数点字号跟随整数/小数，默认true
)
